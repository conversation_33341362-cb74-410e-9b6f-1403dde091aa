<ut-page vue-id="36762d3f-1" class="data-v-4f112eb2" bind:__l="__l" vue-slots="{{['default']}}"><ut-top class="top-warp data-v-4f112eb2" vue-id="{{('36762d3f-2')+','+('36762d3f-1')}}" bg-color="#fff" data-event-opts="{{[['^topHeight',[['getHeight']]]]}}" bind:topHeight="__e" bind:__l="__l" vue-slots="{{['default']}}"><f-navbar vue-id="{{('36762d3f-3')+','+('36762d3f-2')}}" fontColor="#fff" bgColor="{{colors}}" title="校对任务列表" navbarType="1" class="data-v-4f112eb2" bind:__l="__l"></f-navbar><view class="padding-top-sm padding-lr-sm flex align-center data-v-4f112eb2" style="{{'background-color:'+('#fff')+';'}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="u-border margin-right-sm round padding-tb-sm padding-lr-lg data-v-4f112eb2" bindtap="__e"><view class="text-grey text-bold data-v-4f112eb2">{{month}}</view></view><u-input bind:input="__e" vue-id="{{('36762d3f-4')+','+('36762d3f-2')}}" prefixIcon="search" placeholder="输入客户或护理员姓名" shape="circle" border="surround" clearable="{{true}}" value="{{search.key}}" data-event-opts="{{[['^input',[['__set_model',['$0','key','$event',[]],['search']]]]]}}" class="data-v-4f112eb2" bind:__l="__l" vue-slots="{{['suffix']}}"><view slot="suffix" class="data-v-4f112eb2"><u-button vue-id="{{('36762d3f-5')+','+('36762d3f-4')}}" text="搜索" type="success" size="mini" data-event-opts="{{[['^click',[['onSearch']]]]}}" bind:click="__e" class="data-v-4f112eb2" bind:__l="__l"></u-button></view></u-input></view><view class="filter-bar data-v-4f112eb2"><u-tabs vue-id="{{('36762d3f-6')+','+('36762d3f-2')}}" list="{{filterTabs}}" activeColor="{{colors}}" value="{{currentFilter}}" data-event-opts="{{[['^change',[['onFilterChange']]],['^input',[['__set_model',['','currentFilter','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-4f112eb2" bind:__l="__l"></u-tabs></view></ut-top><mescroll-body vue-id="{{('36762d3f-7')+','+('36762d3f-1')}}" top="{{topWrapHeight+'px'}}" top-margin="{{-topWrapHeight+'px'}}" bottom="20" up="{{upOption}}" safearea="{{true}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^down',[['downCallback']]],['^up',[['upCallback']]],['^emptyclick',[['emptyClick']]]]}}" bind:init="__e" bind:down="__e" bind:up="__e" bind:emptyclick="__e" class="data-v-4f112eb2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goToCheck',['$0'],[[['dataList','',index]]]]]]]}}" bindtap="__e" class="data-v-4f112eb2"><view class="item-box data-v-4f112eb2"><view class="head-image data-v-4f112eb2"><block wx:if="{{item.$orig.imgHead}}"><u-lazy-load vue-id="{{('36762d3f-8-'+index)+','+('36762d3f-7')}}" image="{{item.g0}}" width="120" height="160" border-radius="4" class="data-v-4f112eb2" bind:__l="__l"></u-lazy-load></block><block wx:else><text class="cuIcon-people data-v-4f112eb2"></text></block></view><view class="content text-content data-v-4f112eb2"><view class="flex justify-between align-center data-v-4f112eb2"><view class="data-v-4f112eb2"><view class="flex justify-start align-center flex-wrap data-v-4f112eb2"><view class="text-df text-bold margin-right-xs data-v-4f112eb2">{{item.$orig.name}}</view><view class="{{['data-v-4f112eb2',(true)?'cu-tag sm radius light':'',(item.$orig.proofreadError)?'bg-orange':'',(!item.$orig.proofreadError&&item.$orig.isManual)?'bg-green':'',(!item.$orig.proofreadError&&!item.$orig.isManual)?'bg-blue':'']}}">{{''+item.m0+''}}</view></view><view class="text-sm text-gray margin-top-xs data-v-4f112eb2">{{'手机号：'+item.$orig.phone+''}}</view><view class="text-sm text-gray margin-top-xs data-v-4f112eb2">{{'护理日期：'+item.$orig.workDate+''}}</view></view></view><view class="text-sm text-gray margin-top-xs data-v-4f112eb2">{{'护理员：'+item.$orig.attendantName+"（"+item.$orig.groupName+'）'}}</view><block wx:if="{{item.$orig.isManual}}"><view class="text-sm text-gray margin-top-xs data-v-4f112eb2">{{'校对人：'+item.$orig.lastManualUserName+''}}</view><view class="text-sm text-gray margin-top-xs data-v-4f112eb2">{{'校对时间：'+item.$orig.lastManualTime+''}}</view></block></view></view></view></block></mescroll-body><u-datetime-picker vue-id="{{('36762d3f-9')+','+('36762d3f-1')}}" show="{{showMonthPicker}}" mode="year-month" title="选择月份" closeOnClickOverlay="{{true}}" value="{{monthValue}}" data-event-opts="{{[['^confirm',[['onMonthConfirm']]],['^close',[['e1']]],['^cancel',[['e2']]],['^input',[['__set_model',['','monthValue','$event',[]]]]]]}}" bind:confirm="__e" bind:close="__e" bind:cancel="__e" bind:input="__e" class="data-v-4f112eb2" bind:__l="__l"></u-datetime-picker><ut-login-modal vue-id="{{('36762d3f-10')+','+('36762d3f-1')}}" colors="{{colors}}" class="data-v-4f112eb2" bind:__l="__l"></ut-login-modal></ut-page>