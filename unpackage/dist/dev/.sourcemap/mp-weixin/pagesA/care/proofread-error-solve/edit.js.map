{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/proofread-error-solve/edit.vue?fc89", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/proofread-error-solve/edit.vue?b8fe", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/proofread-error-solve/edit.vue?b158", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/proofread-error-solve/edit.vue?7849", "uni-app:///pagesA/care/proofread-error-solve/edit.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/proofread-error-solve/edit.vue?84cf", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/proofread-error-solve/edit.vue?626b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "ProjectItem", "ProjectSelector", "data", "colors", "topWrapHeight", "workId", "month", "loading", "form", "datas", "showProjectPopup", "projectData", "initialProjects", "computed", "community", "uploadInfo", "headers", "Token", "orderProjects", "onLoad", "onShow", "methods", "getHeight", "getInfo", "params", "communityId", "projects", "item", "id", "projectId", "projectName", "govCode", "requireMinDuration", "requireMaxDuration", "_id", "save", "dataId", "urls", "projectIds", "setTimeout", "goBack", "uni", "loadProjectData", "addProject", "resetProjects", "title", "content", "success", "selectProject", "projectCode", "remark", "getProjectActionOptions", "text", "code", "style", "backgroundColor", "projectActionClick", "removeProject", "uploadFaceSuccess", "res", "detail", "name", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,aAAa,qLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,aAAa,+UAEN;AACP,KAAK;AACL;AACA,aAAa,6WAEN;AACP,KAAK;AACL;AACA,aAAa,wPAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AAAgsB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC8MptB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAKA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QAAA;MAAA;IACA;EAAA,EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;MAAAjB;IAAA;IACA;EACA;EACAkB;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACAC;kBACAC;kBACApB;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAAH;gBACA,2DACA,aACAA;kBACAwB;oBAAA,uCACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBAAA;kBAAA,CACA;gBAAA,EACA;gBACA;gBACA;gBACA;kBACA;gBACA;gBACA;kBACA;oBACAP;sBACA;wBACA;sBACA;oBACA;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAQ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAEA1B;kBAAA,OACAkB;oBAAA;sBACAS;sBACAC;wBAAA;sBAAA;oBACA;kBAAA;gBAAA,EACA;gBAAA;gBAAA,OACA;kBACAZ;kBACApB;kBACAiC;oBAAA;kBAAA;kBACA7B;gBACA;cAAA;gBACA;gBACA8B;kBAAA;gBAAA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAjB;kBACApB;gBACA;cAAA;gBAAA;gBAHAH;gBAIA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAyC;MACA;MACA;IACA;IACAC;MAAA;MACAH;QACAI;QACAC;QACAC;UACA;YAAA;YACA;cACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QACA;MACA;MACA;MACAtB;QACA;UAAA;QAAA;QACA;UACA;YACAE;YACAC;YACAC;YACAmB;YACAlB;YACAC;YACAC;YACAiB;UACA;QACA;MACA;IACA;IACAC;MACA;QACAC;QACAC;QACAC;UACAC;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;UAAA;QAAA;QACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;QACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrZA;AAAA;AAAA;AAAA;AAA+0C,CAAgB,ypCAAG,EAAC,C;;;;;;;;;;;ACAn2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/care/proofread-error-solve/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/care/proofread-error-solve/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=68c0200c&scoped=true&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&id=68c0200c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"68c0200c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/care/proofread-error-solve/edit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=68c0200c&scoped=true&\"", "var components\ntry {\n  components = {\n    utPage: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-page/ut-page\" */ \"@/components/ut/ut-page/ut-page.vue\"\n      )\n    },\n    utTop: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-top/ut-top\" */ \"@/components/ut/ut-top/ut-top.vue\"\n      )\n    },\n    fNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/f-navbar/f-navbar\" */ \"@/components/f-navbar/f-navbar.vue\"\n      )\n    },\n    uLazyLoad: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-lazy-load/u-lazy-load\" */ \"@/components/uview-ui/components/u-lazy-load/u-lazy-load.vue\"\n      )\n    },\n    utImageUpload: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-image-upload/ut-image-upload\" */ \"@/components/ut/ut-image-upload/ut-image-upload.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uSwipeAction: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-swipe-action/u-swipe-action\" */ \"@/components/uview-ui/components/u-swipe-action/u-swipe-action.vue\"\n      )\n    },\n    uSwipeActionItem: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-swipe-action-item/u-swipe-action-item\" */ \"@/components/uview-ui/components/u-swipe-action-item/u-swipe-action-item.vue\"\n      )\n    },\n    utFixed: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-fixed/ut-fixed\" */ \"@/components/ut/ut-fixed/ut-fixed.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.form.imgHead ? _vm.$tools.showImg(_vm.form.imgHead) : null\n  var g1 = _vm.datas && _vm.datas.length > 0\n  var l0 = _vm.__map(_vm.orderProjects, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.getProjectActionOptions(item)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g2 = !_vm.form.projects || !_vm.form.projects.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showProjectPopup = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\n    <ut-page class=\"page\">\n        <ut-top class=\"top-warp\" bg-color=\"#fff\" @topHeight=\"getHeight\">\n            <f-navbar fontColor=\"#fff\" :bgColor=\"colors\" title=\"校对异常处理\" navbarType=\"1\"></f-navbar>\n        </ut-top>\n\n        <view class=\"padding-lr-xs\">\n            <!-- 客户信息 -->\n            <view class=\"cu-card margin-lr-sm margin-tb-sm radius bg-white flex padding-sm\">\n                <view class=\"flex-sub-0 margin-right-sm\">\n                    <u-lazy-load v-if=\"form.imgHead\"\n                        :image=\"$tools.showImg(form.imgHead)\"\n                        width=\"120\"\n                        height=\"160\"\n                        border-radius=\"4\" />\n                </view>\n                <view class=\"flex-sub text-content\">\n                    <view class=\"flex justify-between align-center\">\n                        <view>\n                            <view class=\"flex justify-start align-center\">\n                                <view class=\"text-df text-bold text-black\">{{ form.name }}</view>\n                                <view class=\"text-blue margin-left-lg\" v-if=\"form.sex == 1\">男\n                                    <text class=\"cuIcon-male\" />\n                                </view>\n                                <view class=\"text-pink margin-left-lg\" v-if=\"form.sex == 2\">女\n                                    <text class=\"cuIcon-female\" />\n                                </view>\n                            </view>\n                            <view class=\"text-gray margin-top-xs\">\n                                <text v-if=\"form.phone\" class=\"cuIcon-phone margin-right-xs\" />\n                                {{ form.phone }}\n                            </view>\n                        </view>\n                        <view class=\"text-right\">\n                            <view class=\"text-gray\">护理日期</view>\n                            <view class=\"text-df text-bold\" :style=\"{ color: colors }\">{{ form.workDate }}</view>\n                        </view>\n                    </view>\n\n                    <view class=\"text-xs text-gray text-cut margin-top-xs\">{{ form.address }}</view>\n                    <view v-if=\"form.attendantName\" class=\"margin-top-xs\">\n                        <text class=\"text-sm\" :style=\"{ color: colors }\">({{ form.groupName }})</text>\n                        <text class=\"text-sm text-gray\">护理员：{{ form.attendantName }}</text>\n                    </view>\n\n                    <view v-if=\"form.idcard\" class=\"flex align-center margin-top-xs\">\n                        <text class=\"text-sm text-gray margin-right-xs\">证件号码：</text>\n                        <text class=\"text-sm text-black\">{{ form.idcard }}</text>\n                    </view>\n\n                    <!-- 额外信息 -->\n                    <view v-if=\"form.schedulingDuration\" class=\"flex align-center margin-top-xs\">\n                        <text class=\"text-sm text-gray margin-right-xs\">排班时长：</text>\n                        <text class=\"text-sm text-black\">{{ form.schedulingDuration }}</text>\n                    </view>\n\n                    <view v-if=\"form.totalDuration\" class=\"flex align-center margin-top-xs\">\n                        <text class=\"text-sm text-gray margin-right-xs\">实际时长：</text>\n                        <text class=\"text-sm text-black\">{{ form.totalDuration }}</text>\n                    </view>\n\n                    <view v-if=\"form.checkInTime\" class=\"flex align-center margin-top-xs\">\n                        <text class=\"text-sm text-gray margin-right-xs\">签到时间：</text>\n                        <text class=\"text-sm text-black\">{{ form.checkInTime }}</text>\n                    </view>\n\n                    <view v-if=\"form.checkOutTime\" class=\"flex align-center margin-top-xs\">\n                        <text class=\"text-sm text-gray margin-right-xs\">签退时间：</text>\n                        <text class=\"text-sm text-black\">{{ form.checkOutTime }}</text>\n                    </view>\n\n                    <view v-if=\"form.lastManualUserName\" class=\"flex align-center margin-top-xs\">\n                        <text class=\"text-sm text-gray margin-right-xs\">校对人：</text>\n                        <text class=\"text-sm text-black\">{{ form.lastManualUserName }}</text>\n                    </view>\n\n                    <view v-if=\"form.lastManualTime\" class=\"flex align-center margin-top-xs\">\n                        <text class=\"text-sm text-gray margin-right-xs\">校对时间：</text>\n                        <text class=\"text-sm text-black\">{{ form.lastManualTime }}</text>\n                    </view>\n\n                    <view v-if=\"form.proofreadErrorRemark\" class=\"margin-top-xs\">\n                        <text class=\"text-sm text-gray\">校对异常信息：</text>\n                        <text class=\"cu-tag sm bg-orange light radius\">\n                            {{ form.proofreadErrorRemark }}\n                        </text>\n                    </view>\n                </view>\n            </view>\n\n            <!-- 服务资料 -->\n            <view v-if=\"datas && datas.length > 0\" class=\"cu-card margin-lr-sm margin-tb-sm radius bg-white padding-sm\">\n                <scroll-view scroll-y=\"true\" class=\"scroll-box\">\n                    <view v-for=\"(item, index) in datas\" :key=\"index\" class=\"text-content padding-bottom-sm\">\n                        <view class=\"text-bold text-lg\">{{ item.title }}</view>\n                        <view v-for=\"(detail, dIndex) in item.details\" :key=\"dIndex\" class=\"padding-left-lg\">\n                            <view class=\"margin-left-xs\">\n                                <text>{{ detail.title }}</text>\n                                <text v-if=\"detail.require\"\n                                    class=\"text-xs margin-left-xs\"\n                                    :style=\"{ color: colors }\">(必填)\n                                </text>\n                                <text v-else class=\"text-xs margin-left-xs\">(选填)</text>\n                            </view>\n                            <view class=\"margin-top-sm\">\n                                <ut-image-upload\n                                    ref=\"upload\"\n                                    name=\"file\"\n                                    v-model=\"detail.files\"\n                                    mediaType=\"image\"\n                                    :colors=\"colors\"\n                                    :max=\"20\"\n                                    :headers=\"headers\"\n                                    :action=\"uploadInfo.server + uploadInfo.single || ''\"\n                                    :preview-image-width=\"1200\"\n                                    :width=\"200\"\n                                    :height=\"160\"\n                                    :border-radius=\"8\"\n                                    :disabled=\"false\"\n                                    :add=\"true\"\n                                    :remove=\"true\"\n                                    @uploadSuccess=\"uploadFaceSuccess($event, detail)\">\n                                </ut-image-upload>\n                            </view>\n                        </view>\n                    </view>\n                </scroll-view>\n            </view>\n\n            <!-- 服务项目 -->\n            <view class=\"cu-card margin-lr-sm margin-tb-sm radius shadow bg-white padding-sm\">\n                <view class=\"flex justify-between align-center margin-bottom-sm\">\n                    <u-button\n                        type=\"error\"\n                        text=\"重置\"\n                        :plain=\"true\"\n                        size=\"small\"\n                        @click=\"resetProjects\"\n                        :custom-style=\"{ marginRight: '20rpx' }\"\n                    />\n                    <u-button\n                        type=\"primary\"\n                        :color=\"colors\"\n                        text=\"添加项目\"\n                        :plain=\"true\"\n                        size=\"small\"\n                        @click=\"addProject\"\n                    />\n                </view>\n                <u-swipe-action ref=\"swipeProjectList\">\n                    <u-swipe-action-item v-for=\"item in orderProjects\" :key=\"item.id\"\n                        :name=\"item.projectId\"\n                        :options=\"getProjectActionOptions(item)\"\n                        @click=\"projectActionClick\">\n                        <view class=\"padding-tb-sm solid-bottom\">\n                            <project-item :detail=\"item\" :colors=\"colors\"></project-item>\n                        </view>\n                    </u-swipe-action-item>\n                </u-swipe-action>\n                <view v-if=\"!form.projects || !form.projects.length\" class=\"text-center padding-xl\">\n                    <text class=\"text-gray\">无服务项目</text>\n                </view>\n            </view>\n\n        </view>\n\n        <!-- 底部保存按钮 -->\n        <ut-fixed safe-area-inset position=\"bottom\" background=\"#fff\">\n            <view class=\"padding-tb-sm padding-lr-lg\">\n                <u-button\n                    type=\"primary\"\n                    :color=\"colors\"\n                    :loading=\"loading\"\n                    @click=\"save\"\n                    text=\"保存\"\n                    shape=\"circle\"\n                ></u-button>\n            </view>\n        </ut-fixed>\n\n        <!-- 项目选择弹窗 -->\n        <u-popup\n            :show=\"showProjectPopup\"\n            mode=\"bottom\"\n            round=\"10\"\n            :closeable=\"true\"\n            :safe-area-inset-bottom=\"false\"\n            :mask-close-able=\"true\"\n            close-icon-pos=\"top-left\"\n            :z-index=\"1025\"\n            :overlay-style=\"{zIndex:998}\"\n            @close=\"showProjectPopup=false\"\n        >\n            <view class=\"text-center padding-tb text-df text-bold text-black\">项目选择</view>\n            <project-selector\n                :project-data=\"projectData\"\n                :selected-projects=\"form.projects\"\n                :colors=\"colors\"\n                @selectProject=\"selectProject\"\n            />\n        </u-popup>\n    </ut-page>\n</template>\n\n<script>\nlet app = getApp()\nimport { mapState } from 'vuex'\nimport ProjectItem from '@/pagesA/components/project-item.vue'\nimport ProjectSelector from '@/pagesA/components/proofread/project-selector.vue'\n\nexport default {\n    components: {\n        ProjectItem,\n        ProjectSelector,\n    },\n    data() {\n        return {\n            colors: '',\n            topWrapHeight: 0,\n            workId: '',\n            month: '',\n            loading: false,\n            form: {},\n            datas: [],\n            showProjectPopup: false,\n            projectData: [],\n            initialProjects: [],\n        }\n    },\n    computed: {\n        ...mapState({\n            community: state => state.init.community,\n            uploadInfo: state => state.init.oss,\n        }),\n        headers() {\n            return {\n                Token: this.uploadInfo.token,\n            }\n        },\n        orderProjects() {\n            return this.form.projects?.toSorted((a, b) => a.govCode - b.govCode) ?? []\n        },\n    },\n    onLoad(options) {\n        this.workId = options.workId || ''\n        this.month = options.month || ''\n    },\n    onShow() {\n        this.setData({ colors: app.globalData.newColor })\n        this.getInfo()\n    },\n    methods: {\n        getHeight(height, statusHeight) {\n            this.topWrapHeight = height\n        },\n        async getInfo() {\n            if (!this.workId) return\n            this.loading = true\n            const params = {\n                communityId: this.community.id,\n                workId: this.workId,\n            }\n            if (this.month) params.month = this.month\n            try {\n                const { data } = await this.$ut.api('mang/care/proofread/task', params)\n                this.form = {\n                    ...this.form,\n                    ...data,\n                    projects: data.projects?.map((item) => ({\n                        ...item,\n                        id: item.projectId,\n                        projectId: item.projectId,\n                        projectName: item.projectName,\n                        govCode: item.govCode,\n                        requireMinDuration: item.requireMinDuration,\n                        requireMaxDuration: item.requireMaxDuration,\n                        _id: item.id,\n                    })),\n                }\n                this.datas = data.datas || []\n                // 保存初始项目状态用于重置功能\n                if (data.projects && data.projects.length > 0) {\n                    this.initialProjects = JSON.parse(JSON.stringify(this.form.projects))\n                }\n                this.datas.forEach(item => {\n                    if (item.details && item.details.length > 0) {\n                        item.details.forEach(detail => {\n                            if (!detail.files) {\n                                this.$set(detail, 'files', [])\n                            }\n                        })\n                    }\n                })\n            } finally {\n                this.loading = false\n            }\n        },\n        async save() {\n            this.loading = true\n            try {\n                const datas = this.datas.flatMap((item) =>\n                    item.details ? item.details.map((detail) => ({\n                        dataId: detail.id,\n                        urls: detail.files.map((file) => file.url),\n                    })) : [],\n                )\n                await this.$ut.api('mang/care/proofread/errorSave', {\n                    communityId: this.community.id,\n                    workId: this.workId,\n                    projectIds: this.form.projects ? this.form.projects.map(p => p.projectId) : [],\n                    datas,\n                })\n                this.$u.toast('保存成功')\n                setTimeout(() => this.goBack(), 500)\n            } finally {\n                this.loading = false\n            }\n        },\n\n        goBack() {\n            uni.navigateBack()\n        },\n        async loadProjectData() {\n            const { data } = await this.$ut.api('mang/care/work/project/allRuleListpg', {\n                communityId: this.community.id,\n                workId: this.workId,\n            })\n            this.projectData = data?.info || []\n        },\n        addProject() {\n            this.loadProjectData()\n            this.showProjectPopup = true\n        },\n        resetProjects() {\n            uni.showModal({\n                title: '确认重置',\n                content: '确定要将服务项目重置为初始状态吗？',\n                success: (res) => {\n                    if (res.confirm) {\n                        if (this.initialProjects?.length > 0) {\n                            this.form.projects = JSON.parse(JSON.stringify(this.initialProjects))\n                        } else {\n                            this.form.projects = []\n                        }\n                    }\n                },\n            })\n        },\n\n        selectProject(projects) {\n            this.showProjectPopup = false\n            if (!this.form.projects) {\n                this.$set(this.form, 'projects', [])\n            }\n            if (!projects || !projects.length) return\n            projects.forEach(project => {\n                const existingProject = this.form.projects.find(p => p.projectId === project.id)\n                if (!existingProject) {\n                    this.form.projects.push({\n                        id: project.id,\n                        projectId: project.id,\n                        projectName: project.name,\n                        projectCode: project.code,\n                        govCode: project.govCode,\n                        requireMinDuration: project.minDuration,\n                        requireMaxDuration: project.maxDuration,\n                        remark: project.remark || '',\n                    })\n                }\n            })\n        },\n        getProjectActionOptions() {\n            const btnDelete = {\n                text: '删除',\n                code: 'delete',\n                style: {\n                    backgroundColor: '#f56c6c',\n                },\n            }\n            return [btnDelete]\n        },\n        projectActionClick(data) {\n            if (data.code === 'delete') {\n                this.removeProject(data.name)\n            }\n            this.$refs['swipeProjectList'].closeAll()\n        },\n        removeProject(projectId) {\n            if (this.form.projects && this.form.projects.length) {\n                const index = this.form.projects.findIndex(p => p.projectId === projectId)\n                if (index >= 0) {\n                    this.form.projects.splice(index, 1)\n                }\n            }\n        },\n        uploadFaceSuccess(res, detail) {\n            res.forEach((img) => {\n                const item = img.data\n                detail.files.push({\n                    name: item.source,\n                    url: `${this.uploadInfo.preview}?file=${item.name}${item.ext}`,\n                })\n            })\n        },\n    },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page {\n  padding-bottom: 128rpx;\n}\n\n.pop-title {\n  padding-top: 20rpx;\n  text-align: center;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&id=68c0200c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&id=68c0200c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754361564250\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}