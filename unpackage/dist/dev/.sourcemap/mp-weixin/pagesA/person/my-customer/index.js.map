{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/person/my-customer/index.vue?759a", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/person/my-customer/index.vue?cb56", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/person/my-customer/index.vue?13a6", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/person/my-customer/index.vue?7313", "uni-app:///pagesA/person/my-customer/index.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/person/my-customer/index.vue?a7d0", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/person/my-customer/index.vue?5ed5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "components", "MescrollBody", "CustomerItem", "options", "styleIsolation", "data", "colors", "topWrapHeight", "upOption", "noMoreSize", "empty", "icon", "tip", "pageReq", "pagesize", "pageindex", "key", "firstLoad", "dataList", "onShow", "computed", "comm<PERSON>ey", "token", "userInfo", "community", "onLoad", "methods", "getHeight", "downCallback", "upCallback", "communityId", "module", "setTimeout", "emptyClick", "select", "customerId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,aAAa,qLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,4RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAisB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACmCrtB;AAEA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAMA;EACAC;EAAA;EACAC;IACAC;IACAC;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QAAA;QACAC;UACAC;UACAC;QACA;MACA;;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;MAAAb;IAAA;EACA;EACAc,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CAEA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAC;IACAC;MACA;IACA;IACA,YACAC;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAEA;kBACAC;kBACAC;gBAAA,GACA,eACA;kBAAA;kBACAC;oBACA;kBACA;kBACA;kBAEA;kBACA;kBACA;gBAEA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC,mCAEA;IACAC;MACA;QAAAC;MAAA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAAg1C,CAAgB,0pCAAG,EAAC,C;;;;;;;;;;;ACAp2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/person/my-customer/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/person/my-customer/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=15ec7c1c&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=15ec7c1c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"15ec7c1c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/person/my-customer/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=15ec7c1c&scoped=true&\"", "var components\ntry {\n  components = {\n    utPage: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-page/ut-page\" */ \"@/components/ut/ut-page/ut-page.vue\"\n      )\n    },\n    utTop: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-top/ut-top\" */ \"@/components/ut/ut-top/ut-top.vue\"\n      )\n    },\n    fNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/f-navbar/f-navbar\" */ \"@/components/f-navbar/f-navbar.vue\"\n      )\n    },\n    utLoginModal: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-login-modal/ut-login-modal\" */ \"@/components/ut/ut-login-modal/ut-login-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<ut-page>\n\t\t<ut-top class=\"top-warp\" bg-color=\"#fff\" @topHeight=\"getHeight\">\n\t\t\t<f-navbar fontColor=\"#fff\" :bgColor=\"colors\" title=\"我的申请客户\" navbarType=\"1\"></f-navbar>\n\t\t</ut-top>\n\t\t<mescroll-body\r\n\t\t\tref=\"mescrollRef\"\n\t\t\t:top=\"topWrapHeight+'px'\"\n\t\t\t:top-margin=\"-topWrapHeight+'px'\"\n\t\t\tbottom=\"0\"\n\t\t\t:up=\"upOption\"\r\n\t\t\t:safearea=\"true\"\n\t\t\t@init=\"mescrollInit\"\n\t\t\t@down=\"downCallback\"\n\t\t\t@up=\"upCallback\"\n\t\t\t@emptyclick=\"emptyClick\"\n\t\t>\n\n\t\t\t<template v-for=\"(item,index) in dataList\">\t\r\n\t\t\t\t<view @click=\"select(item)\">\r\n\t\t\t\t\t<customer-item :key=\"index\" :detail=\"item\" :colors=\"colors\">\r\n\t\t\t\t\t\t<template #op>\r\n\t\t\t\t\t\t\t<text></text>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</customer-item>\r\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t</mescroll-body>\n\t\t\n\t\t<ut-login-modal :colors=\"colors\"></ut-login-modal>\n\t</ut-page>\n</template>\n\n<script>\nvar app = getApp()\nimport { mapState } from 'vuex'\nimport MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'\nimport MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'\r\nimport CustomerItem from '@/pagesA/components/customer-item.vue'\n\nexport default {\n\tmixins: [MescrollMixin], // 使用mixin\n\tcomponents: {\n\t\tMescrollBody,\r\n\t\tCustomerItem,\n\t},\n\toptions: {\n\t\tstyleIsolation: 'shared',\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tcolors: '',\r\n\t\t\ttopWrapHeight: 0,\r\n\t\t\tupOption: {\r\n\t\t\t\tnoMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5\r\n\t\t\t\tempty: {\r\n\t\t\t\t\ticon: require('@/pagesA/components/image/nodata.png'),\r\n\t\t\t\t\ttip: '~ 没有数据 ~', // 提示\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\tpageReq: {\r\n\t\t\t\tpagesize: 10,\r\n\t\t\t\tpageindex: 1,\r\n\t\t\t\tkey: '',\r\n\t\t\t},\r\n\t\t\tfirstLoad:true,\r\n\t\t\tdataList:[],\t\n\t\t}\n\t},\n\tonShow() {\n\t\tthis.setData({ colors: app.globalData.newColor })\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\tcommKey: state => state.init.template.commKey,\n\t\t\ttoken: state => state.user.token,\n\t\t\tuserInfo: state => state.user.info,\n\t\t\tcommunity: state => state.init.community,\n\t\t}),\n\t},\n\tonLoad: async function (options) {\n\t\tawait this.$onLaunched\n\n\t},\n\tmethods: {\n\t\tgetHeight(h) {\n\t\t\tthis.topWrapHeight = h\n\t\t},\n\t\t/*下拉刷新的回调 */\n\t\tdownCallback() {\n\t\t\tthis.pageReq.pageindex = 1\n\t\t\tthis.mescroll.resetUpScroll()\n\t\t},\n\t\tasync upCallback(page) {\n\t\t\tthis.isShow = false\n\n\t\t\tthis.$ut.api('mang/care/customer/listpg', {\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\tmodule:'long',\r\n\t\t\t\t...this.pageReq,\r\n\t\t\t}).then(({data}) => {\t\t\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.mescroll.endBySize(data.info.length, data.record)\r\n\t\t\t\t},this.firstLoad?0:500)\r\n\t\t\t\tthis.firstLoad=false\r\n\t\t\t\t\n\t\t\t\tif (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表\r\n\t\t\t\tthis.pageReq.pageindex++\n\t\t\t\tthis.dataList = this.dataList.concat(data.info)\t\n\n\t\t\t}).catch(e => {\n\t\t\t\tthis.pageReq.pageindex--\n\t\t\t\tthis.mescroll.endErr()\n\t\t\t})\n\t\t},\n\n\t\temptyClick() {\n\n\t\t},\r\n\t\tselect(item) {\r\n\t\t\tthis.$tools.routerTo('/pagesA/person/my-customer/info', { customerId: item.id })\r\n\t\t},\n\n\t},\n}\n</script>\n<style>\n\n\n</style>\n<style lang=\"scss\" scoped>\r\n.page {\r\n\tbackground-color: #fff;\r\n}\r\n\r\n\r\n\r\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=15ec7c1c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=15ec7c1c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360665668\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}