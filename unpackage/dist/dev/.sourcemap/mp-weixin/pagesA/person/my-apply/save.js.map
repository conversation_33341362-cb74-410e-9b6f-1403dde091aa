{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/person/my-apply/save.vue?0dda", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/person/my-apply/save.vue?68bd", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/person/my-apply/save.vue?77c2", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/person/my-apply/save.vue?b078", "uni-app:///pagesA/person/my-apply/save.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "PickDate", "options", "styleIsolation", "data", "colors", "noClick", "topWrapHeight", "form", "birthday", "rules", "showSex", "sexName", "actions", "id", "name", "educationShow", "educationColumns", "marriageShow", "marriageColumns", "liveStateShow", "liveStateColumns", "politicsShow", "politicsColumns", "religionShow", "religionColumns", "onShow", "onReady", "computed", "comm<PERSON>ey", "token", "userInfo", "community", "onLoad", "methods", "getHeight", "endYear", "sexSelect", "birthdaySelect", "educationSelect", "marriageSelect", "liveStateSelect", "politicsSelect", "religionSelect", "chooseLocation", "uni", "success", "getPersonInfo", "communityId", "obj", "save", "setTimeout", "title"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;;;AAGnD;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,aAAa,qLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,aAAa,+RAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,aAAa,+UAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1IA;AAAA;AAAA;AAAA;AAAgsB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACoGptB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAIA;EACAC;IACAC;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MAEAC;QACAC;MACA;MACAC,QAEA;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA,EACA;MAEAC;MACAC;MAEAC;MACAC;MAEAC;MACAC;MAEA;MACA;;MAEAC;MACAC;MAEAC;MACAC;IACA;EACA;EAEAC;IACA;MAAArB;IAAA;EACA;EACAsB;IACA;IACA;EACA;EACAC,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GAEA;EACAC;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAEA;gBACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CAEA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MACAC;QACAC;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;kBACAlC;gBACA;cAAA;gBAAA;gBAHAV;gBAIA;gBACA;kBACA6C;oBAAA;kBAAA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MAAA;MACA;QACAF;MAAA,GACA,WACA;QACA;UACA;QACA;UACA;QACA;QAGAG;UACAN;YACAO;UACA;QACA;MAEA;IACA;EACA;AACA;AAAA,2B", "file": "pagesA/person/my-apply/save.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/person/my-apply/save.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./save.vue?vue&type=template&id=1882398e&scoped=true&\"\nvar renderjs\nimport script from \"./save.vue?vue&type=script&lang=js&\"\nexport * from \"./save.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1882398e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/person/my-apply/save.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./save.vue?vue&type=template&id=1882398e&scoped=true&\"", "var components\ntry {\n  components = {\n    utPage: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-page/ut-page\" */ \"@/components/ut/ut-page/ut-page.vue\"\n      )\n    },\n    utTop: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-top/ut-top\" */ \"@/components/ut/ut-top/ut-top.vue\"\n      )\n    },\n    fNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/f-navbar/f-navbar\" */ \"@/components/f-navbar/f-navbar.vue\"\n      )\n    },\n    \"u-Form\": function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u--form/u--form\" */ \"@/components/uview-ui/components/u--form/u--form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-form-item/u-form-item\" */ \"@/components/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u--input/u--input\" */ \"@/components/uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-input/u-input\" */ \"@/components/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    \"u-Textarea\": function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u--textarea/u--textarea\" */ \"@/components/uview-ui/components/u--textarea/u--textarea.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uActionSheet: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-action-sheet/u-action-sheet\" */ \"@/components/uview-ui/components/u-action-sheet/u-action-sheet.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-picker/u-picker\" */ \"@/components/uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = new Date().getFullYear()\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showSex = true\n    }\n    _vm.e1 = function ($event) {\n      return _vm.$refs.birthdayPicker.show()\n    }\n    _vm.e2 = function ($event) {\n      _vm.educationShow = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.marriageShow = true\n    }\n    _vm.e4 = function ($event) {\n      _vm.liveStateShow = true\n    }\n    _vm.e5 = function ($event) {\n      _vm.politicsShow = true\n    }\n    _vm.e6 = function ($event) {\n      _vm.religionShow = true\n    }\n    _vm.e7 = function ($event) {\n      _vm.showSex = false\n    }\n    _vm.e8 = function ($event) {\n      _vm.educationShow = false\n    }\n    _vm.e9 = function ($event) {\n      _vm.marriageShow = false\n    }\n    _vm.e10 = function ($event) {\n      _vm.liveStateShow = false\n    }\n    _vm.e11 = function ($event) {\n      _vm.politicsShow = false\n    }\n    _vm.e12 = function ($event) {\n      _vm.religionShow = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./save.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./save.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<ut-page>\r\n\t\t<ut-top class=\"top-warp\" bg-color=\"#fff\" @topHeight=\"getHeight\">\r\n\t\t\t<f-navbar fontColor=\"#fff\" :bgColor=\"colors\" title=\"申请\" navbarType=\"1\" />\r\n\t\t</ut-top>\r\n\t\t<view class=\"padding-lr bg-white\">\r\n\t\t\t<u--form ref=\"form\" labelPostion=\"left\" :model=\"form\" :rules=\"rules\" labelWidth=\"auto\" :labelStyle=\"{color:'#a0a0a0'}\">\r\n\t\t\t\t<u-form-item label=\"姓名\" prop=\"name\" borderBottom>\r\n\t\t\t\t\t<u--input v-model=\"form.name\" border=\"none\" inputAlign='center'/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"性别\" prop=\"sex\" borderBottom @click=\"showSex=true\">\r\n\t\t\t\t\t<u--input v-model=\"sexName\" border=\"none\" disabled disabledColor=\"#ffffff\" placeholder=\"请选择性别\" inputAlign='center' />\r\n\t\t\t\t\t<u-icon slot=\"right\" name=\"arrow-right\" />\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"电话\" prop=\"phone\" borderBottom>\r\n\t\t\t\t\t<u--input v-model=\"form.phone\" border=\"none\" inputAlign='center'/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"证件号\" prop=\"idcard\" borderBottom>\r\n\t\t\t\t\t<u--input v-model=\"form.idcard\" border=\"none\" inputAlign='center'/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"出生日期\" prop=\"birthday\" borderBottom  @click=\"$refs.birthdayPicker.show()\">\r\n\t\t\t\t\t<u--input v-model=\"form.birthday\" border=\"none\" disabled disabledColor=\"#ffffff\" inputAlign='center' />\r\n\t\t\t\t\t<u-icon slot=\"right\" name=\"arrow-right\"/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"民族\" prop=\"nation\" borderBottom>\r\n\t\t\t\t\t<u--input v-model=\"form.nation\" border=\"none\" inputAlign='center'/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"学历\" prop=\"education\" borderBottom  @click=\"educationShow=true\">\r\n\t\t\t\t\t<u--input v-model=\"form.education\" border=\"none\" disabled disabledColor=\"#ffffff\" inputAlign='center' />\r\n\t\t\t\t\t<u-icon slot=\"right\" name=\"arrow-right\"/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"婚姻\" prop=\"marriage\" borderBottom  @click=\"marriageShow=true\">\r\n\t\t\t\t\t<u--input v-model=\"form.marriage\" border=\"none\" disabled disabledColor=\"#ffffff\" inputAlign='center' />\r\n\t\t\t\t\t<u-icon slot=\"right\" name=\"arrow-right\"/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"居住状态\" prop=\"liveState\" borderBottom  @click=\"liveStateShow=true\">\r\n\t\t\t\t\t<u--input v-model=\"form.liveState\" border=\"none\" disabled disabledColor=\"#ffffff\" inputAlign='center' />\r\n\t\t\t\t\t<u-icon slot=\"right\" name=\"arrow-right\"/>\r\n\t\t\t\t</u-form-item>\r\n<!-- \t\t\t\t<u-form-item label=\"失能等级\" prop=\"disabilityLevel\" borderBottom  @click=\"disabilityLevelShow=true\">\r\n\t\t\t\t\t<u--input v-model=\"form.disabilityLevel\" border=\"none\" disabled disabledColor=\"#ffffff\" inputAlign='center' />\r\n\t\t\t\t\t<u-icon slot=\"right\" name=\"arrow-right\"/>\r\n\t\t\t\t</u-form-item> -->\r\n\t\t\t\t<u-form-item label=\"政治面貌\" prop=\"politics\" borderBottom  @click=\"politicsShow=true\">\r\n\t\t\t\t\t<u--input v-model=\"form.politics\" border=\"none\" disabled disabledColor=\"#ffffff\" inputAlign='center' />\r\n\t\t\t\t\t<u-icon slot=\"right\" name=\"arrow-right\"/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"方言\" prop=\"dialect\" borderBottom>\r\n\t\t\t\t\t<u--input v-model=\"form.dialect\" border=\"none\" inputAlign='center'/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"宗教信仰\" prop=\"religion\" borderBottom   @click=\"religionShow=true\">\r\n\t\t\t\t\t<u--input v-model=\"form.religion\" border=\"none\" disabled disabledColor=\"#ffffff\" inputAlign='center' />\r\n\t\t\t\t\t<u-icon slot=\"right\" name=\"arrow-right\"/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"地址\" prop=\"address\" borderBottom>\r\n\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t<u-input v-model=\"form.address\" border=\"none\" inputAlign='left' >\r\n\t\t\t\t\t\t<template #suffix>\r\n\t\t\t\t\t\t\t<view @click=\"chooseLocation\"><u-icon name=\"map\" :color=\"colors\" size=\"38rpx\" /></view>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</u-input>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef MP -->\r\n\t\t\t\t\t<u--input v-model=\"form.address\" border=\"none\" inputAlign='left' >\r\n\t\t\t\t\t\t<template #suffix>\r\n\t\t\t\t\t\t\t<view @click=\"chooseLocation\"><u-icon name=\"map\" :color=\"colors\" size=\"38rpx\" /></view>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</u--input>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item v-if=\"form.longitude && form.latitude\" label=\"位置坐标\" borderBottom>\r\n\t\t\t\t\t<text>{{form.longitude}},{{form.latitude}}</text>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"备注\" prop=\"remark\" borderBottom>\r\n\t\t\t\t\t<u--textarea v-model=\"form.remark\" confirm-type=\"done\"></u--textarea>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t</u--form>\r\n\t\t</view>\r\n\t\t<view class=\"padding-lr padding-tb bg-white\">\r\n\t\t\t<u-button type=\"primary\" shape=\"circle\" :color=\"colors\" text=\"保存申请\" :plain=\"false\" size=\"normal\" @click=\"save\"/>\r\n\t\t</view>\r\n\t\t<view class=\"bg-white\" style=\"height: var(--safe-area-inset-bottom)\"></view>\r\n\t\t<u-action-sheet :show=\"showSex\" :actions=\"actions\" title=\"请选择性别\" @close=\"showSex = false\" @select=\"sexSelect\"></u-action-sheet>\r\n\t\t<pick-date ref=\"birthdayPicker\" :start-year=\"1901\" :end-year=\"new Date().getFullYear()\" \r\n\t\t\t:time-init=\"0\"\r\n\t\t\t:time-hide=\"[true, true, true, false, false, false]\" \r\n\t\t\t:time-label=\"['年', '月', '日', '时', '分', '秒']\"\r\n\t\t\t@submit=\"birthdaySelect\" />\r\n\t\t<u-picker :show=\"educationShow\" :columns=\"educationColumns\" @cancel=\"educationShow = false\" @confirm=\"educationSelect\"></u-picker>\r\n\t\t<u-picker :show=\"marriageShow\" :columns=\"marriageColumns\" @cancel=\"marriageShow = false\" @confirm=\"marriageSelect\"></u-picker>\r\n\t\t<u-picker :show=\"liveStateShow\" :columns=\"liveStateColumns\" @cancel=\"liveStateShow = false\" @confirm=\"liveStateSelect\"></u-picker>\r\n\t\t<u-picker :show=\"politicsShow\" :columns=\"politicsColumns\" @cancel=\"politicsShow = false\" @confirm=\"politicsSelect\"></u-picker>\r\n\t\t<u-picker :show=\"religionShow\" :columns=\"religionColumns\" @cancel=\"religionShow = false\" @confirm=\"religionSelect\"></u-picker>\r\n\t</ut-page>\r\n\t\r\n\n</template>\n\n<script>\nvar app = getApp()\nimport { mapState } from 'vuex'\r\nimport PickDate from '@/pagesA/components/pickDate.vue'\n\nexport default {\n\tcomponents: {\r\n\t\tPickDate,\n\t},\n\toptions: {\n\t\tstyleIsolation: 'shared',\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tcolors: '',\n\t\t\tnoClick: true,\n\t\t\ttopWrapHeight: 0,\r\n\r\n\t\t\tform:{\r\n\t\t\t\tbirthday:'',\r\n\t\t\t},\r\n\t\t\trules:{\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tshowSex:false,\r\n\t\t\tsexName:'',\r\n\t\t\tactions: [{\r\n\t\t\t\t\tid:1,\r\n\t\t\t\t\tname: '男',\r\n\t\t\t\t},{\t\r\n\t\t\t\t\tid:2,\r\n\t\t\t\t\tname: '女',\r\n\t\t\t\t},{\r\n\t\t\t\t\tid:'',\r\n\t\t\t\t\tname: '保密',\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t\t\r\n\t\t\teducationShow:false,\r\n\t\t\teducationColumns:[['不详','无学历','小学','初中','中专','大专','本科','硕士','博士']],\r\n\t\t\t\r\n\t\t\tmarriageShow:false,\r\n\t\t\tmarriageColumns:[['未知','已婚','离异','丧偶']],\r\n\t\t\t\r\n\t\t\tliveStateShow:false,\r\n\t\t\tliveStateColumns:[['未知','独居','与配偶/伴侣居住','与子女居住','与父母居住','与兄弟姐妹居住','与其他亲属居住','与非亲属关系居住']],\r\n\t\t\t\r\n\t\t\t// disabilityLevelShow:false,\r\n\t\t\t// disabilityLevelColumns:[['未知','独居','与配偶/伴侣居住','与子女居住','与父母居住','与兄弟姐妹居住','与其他亲属居住','与非亲属关系居住']],\r\n\t\t\t\r\n\t\t\tpoliticsShow:false,\r\n\t\t\tpoliticsColumns:[['未知','群众','共青团员','中共党员']],\r\n\t\t\t\r\n\t\t\treligionShow:false,\r\n\t\t\treligionColumns:[['未知','佛教','道教','天主教','基督教','伊斯兰教','其他']],\n\t\t}\n\t},\r\n\n\tonShow() {\n\t\tthis.setData({ colors: app.globalData.newColor })\n\t},\r\n\tonReady() {\r\n\t\t\t//onReady 为uni-app支持的生命周期之一\r\n\t    \tthis.$refs.form.setRules(this.rules)\r\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\tcommKey: state => state.init.template.commKey,\n\t\t\ttoken: state => state.user.token,\n\t\t\tuserInfo: state => state.user.info,\n\t\t\tcommunity: state => state.init.community,\n\t\t}),\n\n\t},\n\tonLoad: async function (options) {\n\t\tawait this.$onLaunched\n\r\n\t\tif (options.personId) {\r\n\t\t\tthis.personId = options.personId\r\n\t\t\tthis.getPersonInfo()\r\n\t\t}\r\n\t\t\n\t},\n\tmethods: {\n\t\tgetHeight(h) {\n\t\t\tthis.topWrapHeight = h\n\t\t},\r\n\t\tendYear(){\r\n\t\t\treturn new Date().getFullYear()\r\n\t\t},\r\n\n\t\tsexSelect(e) {\r\n\t\t\tthis.form.sex = e.id\r\n\t\t\tthis.sexName = e.name\r\n\t\t\tthis.$refs.form.validateField('sex')\r\n\t\t},\r\n\r\n\t\tbirthdaySelect(e){\r\n\t\t\tthis.form.birthday=`${e.year}-${e.month}-${e.day}`\r\n\t\t},\r\n\t\teducationSelect(e){\r\n\t\t\tthis.educationShow=false\r\n\t\t\tthis.form.education=e.value[0]\r\n\t\t},\r\n\t\tmarriageSelect(e){\r\n\t\t\tthis.marriageShow=false\r\n\t\t\tthis.form.marriage=e.value[0]\r\n\t\t},\r\n\t\tliveStateSelect(e){\r\n\t\t\tthis.liveStateShow=false\r\n\t\t\tthis.form.liveState=e.value[0]\r\n\t\t},\r\n\t\tpoliticsSelect(e){\r\n\t\t\tthis.politicsShow=false\r\n\t\t\tthis.form.politics=e.value[0]\r\n\t\t},\r\n\t\treligionSelect(e){\r\n\t\t\tthis.religionShow=false\r\n\t\t\tthis.form.religion=e.value[0]\r\n\t\t},\r\n\t\tchooseLocation(){\r\n\t\t\tlet that=this\r\n\t\t\tuni.chooseLocation({\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tthis.$set(this.form,'address',res.address)\r\n\t\t\t\t\tthis.$set(this.form,'latitude',res.latitude)\r\n\t\t\t\t\tthis.$set(this.form,'longitude',res.longitude)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tasync getPersonInfo(){\r\n\t\t\tif(!this.personId) return\r\n\t\t\tconst {data} = await this.$ut.api('mang/person/info', {\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\tid:this.personId,\r\n\t\t\t})\r\n\t\t\tthis.form=data\r\n\t\t\tif(this.form.sex){\r\n\t\t\t\tconst obj=this.actions.find(u=>u.id==this.form.sex)\r\n\t\t\t\tif(obj)this.sexName=obj.name\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tsave(){\r\n\t\t\tthis.$ut.api('mang/person/save', {\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\t...this.form,\r\n\t\t\t}).then(()=>{\r\n\t\t\t\tif(this.personId){\r\n\t\t\t\t\tthis.$tools.back('getPersonInfo()')\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.$tools.back('downCallback()')\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:'保存成功'\r\n\t\t\t\t\t})\r\n\t\t\t\t},100)\r\n\t\t\t\t\r\n\t\t\t})\r\n\t\t}\n\t},\n}\n</script>\n\r\n\n<style lang=\"scss\" scoped>\r\n\r\n\r\n</style>\n"], "sourceRoot": ""}