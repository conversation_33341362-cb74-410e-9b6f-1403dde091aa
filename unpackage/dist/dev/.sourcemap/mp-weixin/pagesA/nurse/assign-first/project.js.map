{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-first/project.vue?ea73", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-first/project.vue?883d", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-first/project.vue?470e", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-first/project.vue?6db0", "uni-app:///pagesA/nurse/assign-first/project.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-first/project.vue?c7d4", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-first/project.vue?7de6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "components", "MescrollBody", "AssignProjectItem", "options", "styleIsolation", "data", "colors", "topWrapHeight", "upOption", "noMoreSize", "empty", "icon", "tip", "pageReq", "pagesize", "pageindex", "key", "firstLoad", "dataList", "firstId", "showProject", "projectData", "checkboxValue1", "checked", "isEdit", "onShow", "computed", "comm<PERSON>ey", "token", "userInfo", "community", "onLoad", "methods", "getHeight", "downCallback", "upCallback", "communityId", "setTimeout", "emptyClick", "getActionOption", "text", "code", "style", "backgroundColor", "longpress", "console", "select", "deleteProject", "ids", "resolve", "reject", "actionOp", "uni", "title", "content", "success", "handleAdd", "handleSave", "projectIds", "getAllProject", "valChange", "projectId", "monthTimes"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,aAAa,qLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,+UAEN;AACP,KAAK;AACL;AACA,aAAa,6WAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,aAAa,wPAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,2VAEN;AACP,KAAK;AACL;AACA,aAAa,uTAEN;AACP,KAAK;AACL;AACA,aAAa,4RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/GA;AAAA;AAAA;AAAA;AAAmsB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC4EvtB;AAEA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAMA;EACAC;EAAA;EACAC;IACAC;IACAC;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QAAA;QACAC;UACAC;UACAC;QACA;MACA;;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MAAAnB;IAAA;EACA;EACAoB,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cACA;cACA;cAEA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAC;IACAC;MACA;IACA;IACA,YACAC;MACA;MACA;MAEA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;kBACAC;kBACAjB;gBAAA,GACA,eACA;kBAAA;kBACAkB;oBACA;kBACA;kBACA;kBAEA;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC,mCAEA;IACAC;MACA;QACAC;QACAC;QACAC;UACAC;QACA;MACA;MAEA;QACAH;QACAC;QACAC;UACAC;QACA;MACA;MAEA;MAEA;QACAtC;MACA;QACAA;MACA;MAEA;IACA;IACAuC;MACAC;IACA;IACAC;MACA;IAAA,CACA;IAEAC;MAAA;MACA;QACA;UACAX;UACAjB;UACA6B;QACA;UACAC;QACA;UACAC;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACAC;QACAC;QACAC;QACAC;UACA;YACA;cACA;gBACA;kBAAA;gBAAA;gBACA;cACA;YACA;UACA,wBACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACArB;QACAjB;QACAuC;MACA;QACA;QACA;QACArB;UACAe;YACAC;UACA;QACA;MACA;IACA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OACA;kBACAvB;kBACAjB;gBACA;cAAA;gBAAA;gBAHAd;gBAKA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAuD;MACA;QACAxB;QACAjB;QACA0C;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjRA;AAAA;AAAA;AAAA;AAAk1C,CAAgB,4pCAAG,EAAC,C;;;;;;;;;;;ACAt2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/nurse/assign-first/project.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/nurse/assign-first/project.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./project.vue?vue&type=template&id=43c28ec2&scoped=true&\"\nvar renderjs\nimport script from \"./project.vue?vue&type=script&lang=js&\"\nexport * from \"./project.vue?vue&type=script&lang=js&\"\nimport style1 from \"./project.vue?vue&type=style&index=1&id=43c28ec2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"43c28ec2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/nurse/assign-first/project.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project.vue?vue&type=template&id=43c28ec2&scoped=true&\"", "var components\ntry {\n  components = {\n    utPage: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-page/ut-page\" */ \"@/components/ut/ut-page/ut-page.vue\"\n      )\n    },\n    utTop: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-top/ut-top\" */ \"@/components/ut/ut-top/ut-top.vue\"\n      )\n    },\n    fNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/f-navbar/f-navbar\" */ \"@/components/f-navbar/f-navbar.vue\"\n      )\n    },\n    uSwipeAction: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-swipe-action/u-swipe-action\" */ \"@/components/uview-ui/components/u-swipe-action/u-swipe-action.vue\"\n      )\n    },\n    uSwipeActionItem: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-swipe-action-item/u-swipe-action-item\" */ \"@/components/uview-ui/components/u-swipe-action-item/u-swipe-action-item.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    utFixed: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-fixed/ut-fixed\" */ \"@/components/ut/ut-fixed/ut-fixed.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uCheckboxGroup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-checkbox-group/u-checkbox-group\" */ \"@/components/uview-ui/components/u-checkbox-group/u-checkbox-group.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-checkbox/u-checkbox\" */ \"@/components/uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    utLoginModal: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-login-modal/ut-login-modal\" */ \"@/components/ut/ut-login-modal/ut-login-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.dataList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.getActionOption(item)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = _vm.checkboxValue1.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showProject = false\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project.vue?vue&type=script&lang=js&\"", "<template>\n\t<ut-page>\n\t\t<ut-top class=\"top-warp\" bg-color=\"#fff\" @topHeight=\"getHeight\">\n\t\t\t<f-navbar fontColor=\"#fff\" :bgColor=\"colors\" title=\"项目评定\" navbarType=\"1\" />\n\t\t</ut-top>\n\t\t<mescroll-body\r\n\t\t\tref=\"mescrollRef\"\n\t\t\t:top=\"topWrapHeight+'px'\"\n\t\t\t:top-margin=\"-topWrapHeight+'px'\"\n\t\t\tbottom=\"140\"\n\t\t\t:up=\"upOption\"\r\n\t\t\t:safearea=\"true\"\n\t\t\t@init=\"mescrollInit\"\n\t\t\t@down=\"downCallback\"\n\t\t\t@up=\"upCallback\"\n\t\t\t@emptyclick=\"emptyClick\"\n\t\t>\n\t\t\t<u-swipe-action ref=\"swipeUserList\">\n\t\t\t<template v-for=\"(item,index) in dataList\">\t\r\n\t\t\t\t<u-swipe-action-item\r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t:name=\"item.id\"\r\n\t\t\t\t\t:options=\"getActionOption(item)\"\r\n\t\t\t\t\t@longpress=\"longpress(item)\"\r\n\t\t\t\t\t@click=\"actionOp\">\r\n\t\t\t\t\t<assign-project-item :detail=\"item\" :colors=\"colors\" @select=\"select\" :all-project=\"projectData\" :tip=\"item.isSubmit?'已提交':''\" :is-edit=\"isEdit\" @valChange=\"valChange\">\r\n\t\t\t\t\t\t<template #op>\r\n\t\t\t\t\t\t\t<u-button type=\"primary\" shape=\"circle\" :color=\"colors\" :text=\"item.isSubmit?'查看':'选择'\" :plain=\"false\" size=\"small\" @tap=\"select(item)\"/>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</assign-project-item>\r\n\t\t\t\t</u-swipe-action-item>\r\n\t\t\t\t\n\t\t\t</template>\r\n\t\t\t</u-swipe-action>\n\t\t</mescroll-body>\n\t\t\r\n\t\t<ut-fixed safe-area-inset position=\"bottom\" v-if=\"isEdit==true\">\r\n\t\t\t<view class=\"margin bg-white\">\r\n\t\t\t\t<u-button type=\"primary\" shape=\"circle\" :color=\"colors\" text=\"新增项目\" size=\"normal\"  @click=\"handleAdd\"></u-button>\r\n\t\t\t</view>\r\n\t\t</ut-fixed>\r\n\t\t\r\n\t\t<u-popup :show=\"showProject\" mode=\"bottom\" round=\"10\" :closeable=\"true\" :safe-area-inset-bottom=\"false\"\r\n\t\t\t\t :mask-close-able=\"true\" close-icon-pos=\"top-left\" :z-index=\"998\" :overlay-style=\"{zIndex:998}\" @close=\"showProject=false\">\r\n\r\n\t\t\t<view class=\"pop-title\">项目选择</view>\r\n\t\t\t<scroll-view scroll-y=\"true\" class=\"scroll-box\" @touchmove.stop.prevent=\"() => {}\">\r\n\t\t\t\t<view style=\"max-height: 75vh;\">\r\n\t\t\t\t<u-checkbox-group v-model=\"checkboxValue1\"  placement=\"column\">\r\n\t\t\t\t\t<template v-for=\"(item,index) in projectData\">\r\n\t\t\t\t\t\t<view class=\"flex justify-between padding-lr-xl padding-tb-sm\">\r\n\t\t\t\t\t\t\t<view class=\"text-df text-bold\">\r\n\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t\t<text class=\"text-no text-sm text-gray\">({{item.govCode}})</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<u-checkbox v-model=\"item.checked\" :activeColor=\"colors\"  :name=\"item.id\"></u-checkbox>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t</u-checkbox-group>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t\t<view safe-area-inset position=\"bottom\">\r\n\t\t\t\t<view class=\"margin\">\r\n\t\t\t\t\t<u-button type=\"primary\" shape=\"circle\" :color=\"colors\" text=\"增加选中\" size=\"normal\" :disabled=\"!checkboxValue1.length\"  @click=\"handleSave\"></u-button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t\t\n\t\t<ut-login-modal :colors=\"colors\"></ut-login-modal>\n\t</ut-page>\n</template>\n\n<script>\nvar app = getApp()\nimport { mapState } from 'vuex'\nimport MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'\nimport MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'\r\nimport AssignProjectItem from '@/pagesA/components/assign-project-item.vue'\n\nexport default {\n\tmixins: [MescrollMixin], // 使用mixin\n\tcomponents: {\n\t\tMescrollBody,\r\n\t\tAssignProjectItem,\n\t},\n\toptions: {\n\t\tstyleIsolation: 'shared',\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tcolors: '',\n\t\t\ttopWrapHeight: 0,\n\t\t\tupOption: {\n\t\t\t\tnoMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5\n\t\t\t\tempty: {\n\t\t\t\t\ticon: require('@/pagesA/components/image/nodata.png'),\n\t\t\t\t\ttip: '~ 没有数据 ~', // 提示\n\t\t\t\t},\n\t\t\t},\n\t\t\tpageReq: {\n\t\t\t\tpagesize: 50,\n\t\t\t\tpageindex: 1,\n\t\t\t\tkey: '',\n\t\t\t},\r\n\t\t\tfirstLoad:true,\n\t\t\tdataList:[],\n\t\t\tfirstId:'',\r\n\t\t\tshowProject:false,\r\n\t\t\tprojectData:[],\r\n\t\t\tcheckboxValue1:[],\r\n\t\t\tchecked:false,\r\n\t\t\tisEdit:false,\n\t\t}\n\t},\n\tonShow() {\n\t\tthis.setData({ colors: app.globalData.newColor })\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\tcommKey: state => state.init.template.commKey,\n\t\t\ttoken: state => state.user.token,\n\t\t\tuserInfo: state => state.user.info,\n\t\t\tcommunity: state => state.init.community,\n\t\t}),\n\t},\n\tonLoad: async function (options) {\n\t\tawait this.$onLaunched\n\t\tif(options.firstId)this.firstId = options.firstId\t\t\r\n\t\tif (options.isEdit) this.isEdit = options.isEdit =='true'\r\n\t\t\r\n\t\tthis.getAllProject()\n\t},\n\tmethods: {\n\t\tgetHeight(h) {\n\t\t\tthis.topWrapHeight = h\n\t\t},\n\t\t/*下拉刷新的回调 */\n\t\tdownCallback() {\n\t\t\tthis.pageReq.pageindex = 1\n\t\t\tthis.mescroll.resetUpScroll()\r\n\t\t\t\r\n\t\t\tthis.$refs['swipeUserList'].closeAll()\n\t\t},\n\t\tasync upCallback(page) {\r\n\t\t\tif(!this.firstId) return \n\t\t\tthis.$ut.api('mang/nurse/first/project/listpg', {\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\tfirstId:this.firstId,\r\n\t\t\t\t...this.pageReq,\r\n\t\t\t}).then(({data}) => {\t\t\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.mescroll.endBySize(data.info.length, data.record)\r\n\t\t\t\t},this.firstLoad?0:500)\r\n\t\t\t\tthis.firstLoad=false\r\n\t\t\t\t\n\t\t\t\tif (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表\r\n\t\t\t\tthis.pageReq.pageindex++\n\t\t\t\tthis.dataList = this.dataList.concat(data.info)\t\n\t\t\t}).catch(e => {\n\t\t\t\tthis.pageReq.pageindex--\n\t\t\t\tthis.mescroll.endErr()\n\t\t\t})\n\t\t},\n\n\t\temptyClick() {\n\n\t\t},\r\n\t\tgetActionOption(item) {\r\n\t\t\tconst btnSubmitCancel = {\r\n\t\t\t\ttext: '取消项目',\r\n\t\t\t\tcode:'submitCancel',\r\n\t\t\t\tstyle: {\r\n\t\t\t\t\tbackgroundColor: '#ffaa7f'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\tconst btnDelete = {\r\n\t\t\t\ttext: '删除项目',\r\n\t\t\t\tcode:'delete',\r\n\t\t\t\tstyle: {\r\n\t\t\t\t\tbackgroundColor: '#f56c6c'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tlet data = []\r\n\t\r\n\t\t\tif (item.isSubmit) {\r\n\t\t\t\tdata.push(btnSubmitCancel)\r\n\t\t\t} else {\r\n\t\t\t\tdata.push(btnDelete)\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn data\r\n\t\t},\r\n\t\tlongpress(item) {\r\n\t\t\tconsole.log(item)\r\n\t\t},\r\n\t\tselect(item) {\r\n\t\t\t//this.$tools.routerTo('/pagesA/person/my-apply/info', { firstId: item.id })\r\n\t\t},\n\r\n\t\tdeleteProject(id){\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tthis.$ut.api('mang/nurse/first/project/delete', {\r\n\t\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\t\tfirstId:this.firstId,\r\n\t\t\t\t\tids:[id],\r\n\t\t\t\t}).then(res=>{\r\n\t\t\t\t\tresolve(res)\r\n\t\t\t\t}).catch(err=>{\r\n\t\t\t\t\treject(err)\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t},\r\n\t\tactionOp(data) {\r\n\t\t\tlet that = this\r\n\t\t\tlet content = ''\r\n\t\t\tif (data.code == 'delete') content = '确认删除该项目吗？'\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '操作提示',\r\n\t\t\t\tcontent: content,\r\n\t\t\t\tsuccess: res=> {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tif (data.code == 'delete') {\r\n\t\t\t\t\t\t\tthis.deleteProject(data.name).then(()=>{\r\n\t\t\t\t\t\t\t\tlet objIndex = this.dataList.findIndex(u => u.id == data.name)\r\n\t\t\t\t\t\t\t\tthis.dataList.splice(objIndex, 1)\r\n\t\t\t\t\t\t\t})\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$refs['swipeUserList'].closeAll()\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\thandleAdd(){\r\n\t\t\tthis.showProject=true\r\n\t\t},\r\n\t\thandleSave(){\r\n\t\t\tthis.$ut.api('mang/nurse/first/project/add',{\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\tfirstId:this.firstId,\r\n\t\t\t\tprojectIds:this.checkboxValue1,\r\n\t\t\t}).then(()=>{\r\n\t\t\t\tthis.showProject=false\r\n\t\t\t\tthis.downCallback()\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle:'保存成功'\r\n\t\t\t\t\t})\r\n\t\t\t\t},100)\r\n\t\t\t})\r\n\t\t},\r\n\t\tasync getAllProject(){\r\n\t\t\tif(!this.firstId) return \r\n\t\t\tconst {data}= await this.$ut.api('mang/nurse/first/project/allList',{\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\tfirstId:this.firstId,\r\n\t\t\t})\r\n\r\n\t\t\tthis.projectData=data\r\n\t\t},\r\n\t\tvalChange(item){\r\n\t\t\tthis.$ut.api('mang/nurse/first/project/time',{\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\tfirstId:this.firstId,\r\n\t\t\t\tprojectId:item.id,\r\n\t\t\t\tmonthTimes:item.monthTimes,\r\n\t\t\t})\r\n\t\t}\n\t},\n}\n</script>\n<style>\n\n\n</style>\n<style lang=\"scss\" scoped>\r\n.page {\r\n\tbackground-color: #fff;\r\n}\r\n\r\n\r\n.pop-title {\r\n\tpadding-top: 20rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n\r\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project.vue?vue&type=style&index=1&id=43c28ec2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project.vue?vue&type=style&index=1&id=43c28ec2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360673287\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}