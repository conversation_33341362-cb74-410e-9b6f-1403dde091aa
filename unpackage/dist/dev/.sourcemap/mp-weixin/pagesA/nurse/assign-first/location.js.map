{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-first/location.vue?c421", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-first/location.vue?7623", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-first/location.vue?d1df", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-first/location.vue?96e1", "uni-app:///pagesA/nurse/assign-first/location.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "options", "styleIsolation", "data", "colors", "noClick", "firstId", "isEdit", "topWrapHeight", "markers", "circles", "addressInfo", "location", "customerLocation", "gpsCount", "intervalTime", "loadingLocation", "computed", "comm<PERSON>ey", "token", "userInfo", "community", "uploadInfo", "position", "latitude", "longitude", "address", "isGPS", "onShow", "onLoad", "destroyed", "clearInterval", "methods", "getHeight", "getInfo", "uni", "title", "communityId", "openMapSource", "loadGPS", "getLocationInfo", "getLocation", "fillColor", "color", "radius", "strokeWidth", "openMap", "saveLocation", "icon", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;;;AAGvD;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,aAAa,qLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,wPAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAosB,CAAgB,soBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC2CxtB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,eAEA;EACAC,aACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EAAA,EACA;EACAC;IACA;MACAxB;IACA;EACA;EACAyB;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAEA;cACA;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACAC;kBACAC;gBACA;gBAAA;gBAAA,OACA;kBACAC;kBACA/B;gBACA;kBAAA6B;gBAAA;cAAA;gBAAA;gBAHAhC;gBAIA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAmC;MACA;QACAd;QACAC;QACAC;MACA;IACA;IACAa;MAAA;MACA;MACA;MACA;MACAR;MACA;QACA;QACA;QACA;UACAA;UACA;QACA;MACA;IACA;IACAS;MAAA;MACA;QACAtB;QACAO;QACAD;MACA;QACA;MACA;IACA;IACAiB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA;oBACAhB;oBACAD;oBACAkB;oBACAC;oBACAC;oBACAC;kBACA;gBAEA,yBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAV;QACA/B;QACAmB;QACAD;QACAE;MACA;QACAS;UACAC;UACAY;QACA;QACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pagesA/nurse/assign-first/location.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/nurse/assign-first/location.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./location.vue?vue&type=template&id=08954748&\"\nvar renderjs\nimport script from \"./location.vue?vue&type=script&lang=js&\"\nexport * from \"./location.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/nurse/assign-first/location.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./location.vue?vue&type=template&id=08954748&\"", "var components\ntry {\n  components = {\n    utPage: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-page/ut-page\" */ \"@/components/ut/ut-page/ut-page.vue\"\n      )\n    },\n    utTop: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-top/ut-top\" */ \"@/components/ut/ut-top/ut-top.vue\"\n      )\n    },\n    fNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/f-navbar/f-navbar\" */ \"@/components/f-navbar/f-navbar.vue\"\n      )\n    },\n    utFixed: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-fixed/ut-fixed\" */ \"@/components/ut/ut-fixed/ut-fixed.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./location.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./location.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<ut-page>\r\n\t\t<ut-top class=\"top-warp\" bg-color=\"#fff\" @topHeight=\"getHeight\">\r\n\t\t\t<f-navbar fontColor=\"#fff\" :bgColor=\"colors\" title=\"重定位\" navbarType=\"2\" />\r\n\t\t</ut-top>\r\n\t\t\r\n\t\t<view class=\"bg-white padding-tb-sm padding-lr-sm\" :style=\"{minHeight:'calc(100vh - '+topWrapHeight+'px)'}\">\r\n\t\t\t<view class=\"margin-top-lg padding-lr-sm text-content\">\r\n\t\t\t\t<view v-if=\"customerLocation.longitude\">\r\n\t\t\t\t\t<view v-if=\"customerLocation.longitude\" @tap=\"openMapSource\" class=\"text-gray\">点击这里查看原始坐标</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else>未记录有任何定位信息</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"padding-lr-sm\">\r\n\t\t\t\t<map style=\"width:100%;height:600rpx\" id=\"myMap\" :scale=\"18\" :latitude=\"location.latitude\" :longitude=\"location.longitude\" :markers=\"markers\" :circles=\"circles\" show-location></map>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"isGPS\" class=\"margin-top-lg padding-lr-sm text-content\">\r\n\t\t\t\t<view v-if=\"addressInfo && addressInfo.address\" class=\"text-content\">当前住置：{{addressInfo.address}}</view>\r\n\t\t\t\t<view v-if=\"addressInfo.location.longitude\">\r\n\t\t\t\t\t坐标：{{addressInfo.location.longitude}},{{addressInfo.location.latitude}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"addressInfo.location.longitude\" class=\"text-gray\" @tap=\"openMap\">地图上显示</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-else>未获取当前信息,请稍等。如果长时间未获得信息，请检查定位权限是否开启。</view>\r\n\t\t\t<view class=\"text-center\">\r\n\t\t\t\t<view v-if=\"loadingLocation==1\">正在获取定位</view>\r\n\t\t\t\t<view v-else-if=\"loadingLocation==2\">获取定位完成</view>\r\n\t\t\t\t<view v-if=\"loadingLocation==2\" @click=\"loadGPS\">重新获取</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<ut-fixed safe-area-inset position=\"bottom\" background=\"#fff\" v-if=\"isEdit==true\">\r\n\t\t\t<view class=\"padding-tb-sm padding-lr-lg\">\r\n\t\t\t\t<u-button type=\"primary\" shape=\"circle\" :disabled=\"!isGPS\" :color=\"colors\" text=\"保存位置\" size=\"normal\"  @click=\"saveLocation\"></u-button>\r\n\t\t\t</view>\r\n\t\t</ut-fixed>\r\n\t</ut-page>\n</template>\n\n<script>\n\nvar app = getApp()\nimport { mapState } from 'vuex'\nexport default {\n\tcomponents: {\n\t},\n\toptions: {\n\t\tstyleIsolation: 'shared',\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tcolors: '',\n\t\t\tnoClick: true,\n\t\t\tfirstId:'',\r\n\t\t\tisEdit:false,\r\n\t\t\ttopWrapHeight:0,\r\n\t\t\t\r\n\t\t\tmarkers:[],\r\n\t\t\tcircles:[],\r\n\t\t\taddressInfo:{},\r\n\t\t\tlocation:{},\r\n\t\t\tcustomerLocation:{},\r\n\t\t\tgpsCount:0,\r\n\t\t\tintervalTime:{},\r\n\t\t\tloadingLocation:0,\n\t\t}\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\tcommKey: state => state.init.template.commKey,\n\t\t\ttoken: state => state.user.token,\n\t\t\tuserInfo: state => state.user.info,\n\t\t\tcommunity: state => state.init.community,\n\t\t\tuploadInfo: state => state.init.oss,\n\t\t}),\r\n\t\tposition() {\r\n\t\t\treturn {\r\n\t\t\t\tlatitude: this.location.latitude,\r\n\t\t\t\tlongitude: this.location.longitude,\r\n\t\t\t\taddress: '当前位置',\r\n\t\t\t}\r\n\t\t},\r\n\t\tisGPS(){\r\n\t\t\tif(this.location && this.location.longitude && this.location.latitude) return true\r\n\t\t\treturn false\r\n\t\t},\n\t},\n\tonShow() {\n\t\tthis.setData({\n\t\t\tcolors: app.globalData.newColor,\n\t\t})\n\t},\n\tonLoad: async function (options) {\n\t\tawait this.$onLaunched\n\n\t\tif (options.firstId) this.firstId = options.firstId\t\t\r\n\t\tif (options.isEdit) this.isEdit = options.isEdit =='true'\r\n\t\t\r\n\t\tthis.getInfo()\r\n\t\tthis.loadGPS()\n\t},\r\n\tdestroyed() {\r\n\t\tclearInterval(this.intervalTime)\r\n\t},\n\tmethods: {\n\t\tgetHeight(h) {\r\n\t\t\tthis.topWrapHeight = h\r\n\t\t},\r\n\t\tasync getInfo(){\r\n\t\t\tif (!this.firstId) return\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle:'请稍等...'\r\n\t\t\t})\r\n\t\t\tconst {data} = await this.$ut.api('mang/nurse/first/location/info', {\r\n\t\t\t\tcommunityId: this.community.id,\r\n\t\t\t\tfirstId: this.firstId,\r\n\t\t\t}).finally(()=>{uni.hideLoading()})\r\n\t\t\tthis.customerLocation=data\r\n\t\t},\r\n\t\t\r\n\t\topenMapSource(){\r\n\t\t\tthis.$wxsdk.openLocation({\r\n\t\t\t\tlatitude: this.customerLocation.latitude,\r\n\t\t\t\tlongitude: this.customerLocation.longitude,\r\n\t\t\t\taddress: this.customerLocation.address,\r\n\t\t\t})\r\n\t\t},\r\n\t\tloadGPS(){\r\n\t\t\tthis.gpsCount=0;\r\n\t\t\tthis.getLocation()\r\n\t\t\tthis.loadingLocation=1\r\n\t\t\tclearInterval(this.intervalTime)\r\n\t\t\tthis.intervalTime = setInterval(() => {\r\n\t\t\t  this.getLocation();  // 接口方法\r\n\t\t\t  this.gpsCount++\r\n\t\t\t  if(this.gpsCount>5){\r\n\t\t\t\t  clearInterval(this.intervalTime)\r\n\t\t\t\t  this.loadingLocation=2\r\n\t\t\t  }\r\n\t\t\t}, 3000);\r\n\t\t},\r\n\t\tgetLocationInfo(location){\r\n\t\t\tthis.$ut.api('comm/locationInfo',{\r\n\t\t\t\tcommKey:this.commKey,\r\n\t\t\t\tlongitude:location.longitude,\r\n\t\t\t\tlatitude:location.latitude\r\n\t\t\t}).then((res)=>{\r\n\t\t\t\tthis.addressInfo=res.data\r\n\t\t\t})\r\n\t\t},\r\n\t\tasync getLocation() {\r\n\t\t\tthis.$wxsdk.getLocationToAddress().then(location => {\r\n\t\t\t\tthis.location=location\t\t\t\t\r\n\t\t\t\tthis.circles=[{\r\n\t\t\t\t\t\tlongitude: location.longitude,\r\n\t\t\t\t\t\tlatitude: location.latitude,\r\n\t\t\t\t\t\tfillColor: \"#FF2B431A\",\r\n\t\t\t\t\t\tcolor: \"#FF0000\",\r\n\t\t\t\t\t\tradius: 1,\r\n\t\t\t\t\t\tstrokeWidth: 2\r\n\t\t\t\t\t}]\r\n\t\t\t\t\r\n\t\t\t}).catch(err => {\r\n\t\t\t})\r\n\t\t},\r\n\t\topenMap(){\r\n\t\t\tthis.$wxsdk.openLocation(this.position)\r\n\t\t},\r\n\t\tsaveLocation(){\r\n\t\t\tif (!this.firstId) return\r\n\t\t\tthis.$ut.api('mang/nurse/first/location/set', {\r\n\t\t\t\tcommunityId: this.community.id,\r\n\t\t\t\tfirstId: this.firstId,\r\n\t\t\t\tlongitude:this.location.longitude,\r\n\t\t\t\tlatitude:this.location.latitude,\r\n\t\t\t\taddress:this.addressInfo.address,\r\n\t\t\t}).then(()=>{\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle:'保存成功',\r\n\t\t\t\t\ticon:'none',\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.$tools.back('loadInfo()')\r\n\t\t\t\t},1500)\r\n\t\t\t})\r\n\t\t}\n\t},\n}\n</script>\n\n<style>\n</style>"], "sourceRoot": ""}