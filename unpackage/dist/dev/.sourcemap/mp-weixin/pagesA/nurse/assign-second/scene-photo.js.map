{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/scene-photo.vue?016d", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/scene-photo.vue?6668", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/scene-photo.vue?7b66", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/scene-photo.vue?66fc", "uni-app:///pagesA/nurse/assign-second/scene-photo.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/scene-photo.vue?a184", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/scene-photo.vue?2779"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "options", "styleIsolation", "data", "colors", "noClick", "topWrapHeight", "secondId", "isEdit", "myPhotos", "setInfo", "form", "remark", "computed", "comm<PERSON>ey", "token", "userInfo", "community", "uploadInfo", "headers", "Token", "onShow", "onLoad", "methods", "getHeight", "uploadFaceSuccess", "res", "id", "url", "imgDelete", "console", "getSceneSetInfo", "communityId", "getSceneInfo", "saveScene", "imgs", "uni", "title", "icon", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,aAAa,qLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,aAAa,wPAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAusB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6C3tB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,eAEA;EACAC,aACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MAEAC;MACAC;MACAC;IACA;EACA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;QACAC;MACA;IACA;EAAA,EACA;EACAC;IACA;MACAjB;IACA;EACA;EACAkB;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAEA;cACA;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAC;IACAC;MACA;IACA;IACAC;MAAA;MACAC;QACA;QACA;QACA;UACAC;UACAC;QACA;MACA;IAEA;IACAC;MACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;gBACA;cAAA;gBAAA;gBAFA7B;gBAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA8B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OACA;kBACAD;kBACAzB;gBACA;cAAA;gBAAA;gBAHAJ;gBAIA;gBACAA;kBACA;oBACAwB;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAM;MAAA;MACA;MACA;QACAF;QACAzB;QACAK;QACAuB;MACA;QACAC;UACAC;UACAC;QACA;QACAC;UACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1JA;AAAA;AAAA;AAAA;AAAs1C,CAAgB,gqCAAG,EAAC,C;;;;;;;;;;;ACA12C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/nurse/assign-second/scene-photo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/nurse/assign-second/scene-photo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./scene-photo.vue?vue&type=template&id=42f543be&scoped=true&\"\nvar renderjs\nimport script from \"./scene-photo.vue?vue&type=script&lang=js&\"\nexport * from \"./scene-photo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./scene-photo.vue?vue&type=style&index=0&id=42f543be&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"42f543be\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/nurse/assign-second/scene-photo.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scene-photo.vue?vue&type=template&id=42f543be&scoped=true&\"", "var components\ntry {\n  components = {\n    utPage: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-page/ut-page\" */ \"@/components/ut/ut-page/ut-page.vue\"\n      )\n    },\n    utTop: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-top/ut-top\" */ \"@/components/ut/ut-top/ut-top.vue\"\n      )\n    },\n    fNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/f-navbar/f-navbar\" */ \"@/components/f-navbar/f-navbar.vue\"\n      )\n    },\n    utImageUpload: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-image-upload/ut-image-upload\" */ \"@/components/ut/ut-image-upload/ut-image-upload.vue\"\n      )\n    },\n    \"u-Textarea\": function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u--textarea/u--textarea\" */ \"@/components/uview-ui/components/u--textarea/u--textarea.vue\"\n      )\n    },\n    utFixed: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-fixed/ut-fixed\" */ \"@/components/ut/ut-fixed/ut-fixed.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scene-photo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scene-photo.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<ut-page>\r\n\t\t<ut-top class=\"top-warp\" bg-color=\"#fff\" @topHeight=\"getHeight\">\r\n\t\t\t<f-navbar fontColor=\"#fff\" :bgColor=\"colors\" title=\"现场照片\" navbarType=\"2\" />\r\n\t\t</ut-top>\r\n\t\t\r\n\t\t<view class=\"bg-white padding-tb-sm padding-lr-sm\" :style=\"{minHeight:'calc(100vh - '+topWrapHeight+'px)'}\">\r\n\t\t\t<view v-if=\"setInfo.minCount\" class=\"margin-tb padding-lr-xs\" :style=\"{color:colors}\">至少需要 {{setInfo.minCount}}张现场照片</view>\r\n\t\t\t<view class=\"image-box\">\r\n\t\t\t\t<ut-image-upload\r\n\t\t\t\t\tref=\"upload\"\r\n\t\t\t\t\tname=\"file\"\r\n\t\t\t\t\tv-model=\"myPhotos\"\r\n\t\t\t\t\tmediaType=\"image\"\r\n\t\t\t\t\t:colors=\"colors\"\r\n\t\t\t\t\t:max=\"20\"\r\n\t\t\t\t\t:headers=\"headers\"\r\n\t\t\t\t\t:action=\"uploadInfo.server+uploadInfo.single||''\"\r\n\t\t\t\t\t:preview-image-width=\"1200\"\r\n\t\t\t\t\t:width=\"236\"\r\n\t\t\t\t\t:height=\"180\"\r\n\t\t\t\t\t:border-radius=\"8\"\r\n\t\t\t\t\t:add=\"isEdit==true\"\r\n\t\t\t\t\t:remove=\"isEdit==true\"\r\n\t\t\t\t\t@uploadSuccess=\"uploadFaceSuccess\"\r\n\t\t\t\t\t@imgDelete=\"imgDelete\">\r\n\t\t\t\t</ut-image-upload>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"margin-top-lg text-content\">\r\n\t\t\t\t<view>描述：</view>\r\n\t\t\t\t<u--textarea v-model=\"form.remark\" placeholder=\"请输入内容\" :maxlength=\"500\" count confirmType=\"done\"></u--textarea>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<ut-fixed safe-area-inset position=\"bottom\" background=\"#fff\" v-if=\"isEdit==true\">\r\n\t\t\t<view class=\"padding-tb-sm padding-lr-lg\">\r\n\t\t\t\t<u-button type=\"primary\" shape=\"circle\" :color=\"colors\" text=\"保存现场照片\" size=\"normal\"  @click=\"saveScene\"></u-button>\r\n\t\t\t</view>\r\n\t\t</ut-fixed>\r\n\t</ut-page>\n</template>\n\n<script>\n\nvar app = getApp()\nimport { mapState } from 'vuex'\nexport default {\n\tcomponents: {\n\t},\n\toptions: {\n\t\tstyleIsolation: 'shared',\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tcolors: '',\n\t\t\tnoClick: true,\r\n\t\t\ttopWrapHeight:0,\n\t\t\tsecondId:'',\r\n\t\t\tisEdit:false,\r\n\t\t\t\r\n\t\t\tmyPhotos: [],\r\n\t\t\t\r\n\t\t\tsetInfo:{},\r\n\t\t\tform:{},\r\n\t\t\tremark:'',\n\t\t}\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\tcommKey: state => state.init.template.commKey,\n\t\t\ttoken: state => state.user.token,\n\t\t\tuserInfo: state => state.user.info,\n\t\t\tcommunity: state => state.init.community,\n\t\t\tuploadInfo: state => state.init.oss,\n\t\t}),\r\n\t\theaders(){\r\n\t\t\treturn {\r\n\t\t\t\tToken:this.uploadInfo.token\r\n\t\t\t}\r\n\t\t},\n\t},\n\tonShow() {\n\t\tthis.setData({\n\t\t\tcolors: app.globalData.newColor,\n\t\t})\n\t},\n\tonLoad: async function (options) {\n\t\tawait this.$onLaunched\n\n\t\tif (options.secondId) this.secondId = options.secondId\r\n\t\tif (options.isEdit) this.isEdit = options.isEdit =='true'\r\n\t\t\r\n\t\tthis.getSceneSetInfo()\r\n\t\tthis.getSceneInfo()\n\t},\n\tmethods: {\n\t\tgetHeight(h) {\r\n\t\t\tthis.topWrapHeight = h\r\n\t\t},\r\n\t\tuploadFaceSuccess(res) {\r\n\t\t\tres.forEach(img=>{\r\n\t\t\t\tconst item=img.data\r\n\t\t\t\t//this.myPhotos.push(this.uploadInfo.preview + '?file=' + item.name + item.ext)\r\n\t\t\t\tthis.myPhotos.push({\r\n\t\t\t\t\tid:'',\r\n\t\t\t\t\turl:this.uploadInfo.preview + '?file=' + item.name + item.ext,\r\n\t\t\t\t})\r\n\t\t\t})\r\n\r\n\t\t},\r\n\t\timgDelete(e) {\r\n\t\t\tconsole.log(e)\r\n\t\t\t//this.form.imgHead = ''\r\n\t\t},\r\n\t\tasync getSceneSetInfo() {\r\n\t\t\tif (!this.secondId) return\r\n\t\t\tconst {data} = await this.$ut.api('mang/nurse/second/scene/setInfo', {\r\n\t\t\t\tcommunityId: this.community.id\r\n\t\t\t})\r\n\t\t\tthis.setInfo=data\r\n\t\t},\r\n\t\tasync getSceneInfo() {\r\n\t\t\tif (!this.secondId) return\r\n\t\t\tconst {data} = await this.$ut.api('mang/nurse/second/scene/list', {\r\n\t\t\t\tcommunityId: this.community.id,\r\n\t\t\t\tsecondId:this.secondId,\r\n\t\t\t})\r\n\t\t\tthis.form=data\r\n\t\t\tdata.imgs.forEach(item=>{\r\n\t\t\t\tthis.myPhotos.push({\r\n\t\t\t\t\tid:item.id,\r\n\t\t\t\t\turl:item.url,\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t},\r\n\t\tsaveScene(){\r\n\t\t\tif (!this.secondId) return\r\n\t\t\tthis.$ut.api('mang/nurse/second/scene/save', {\r\n\t\t\t\tcommunityId: this.community.id,\r\n\t\t\t\tsecondId:this.secondId,\r\n\t\t\t\tremark:this.form.remark,\r\n\t\t\t\timgs:this.myPhotos,\r\n\t\t\t}).then(()=>{\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle:'保存成功',\r\n\t\t\t\t\ticon:'none',\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.$tools.back('loadInfo()')\r\n\t\t\t\t},1500)\r\n\t\t\t\t//this.$tools.back('getFirstStateInfo')\r\n\t\t\t})\r\n\t\t}\n\t},\n}\n</script>\n\n<style lang=\"scss\" scoped>\r\n\t/deep/.ut-image-upload-list{\r\n\t\tjustify-content: flex-start;\r\n\t\talign-items: center;\r\n\t}\n</style>", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scene-photo.vue?vue&type=style&index=0&id=42f543be&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scene-photo.vue?vue&type=style&index=0&id=42f543be&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360673270\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}